#!/usr/bin/env python3
"""
Script to send iOS-only weekly summary notification to user mcaK5709t3MZAcUpdAeEGrmYgaT2 (Teague).
This script fetches conversations from the past 7 days and generates a comprehensive weekly summary.

Usage:
    python scripts/send_weekly_summary_to_teague.py
"""

import asyncio
import sys
import os
import json
from datetime import datetime, time, timedelta
import threading

# Add the backend directory to the Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Load environment variables from .env file
from dotenv import load_dotenv
load_dotenv()

# Initialize Firebase
import firebase_admin
from firebase_admin import credentials, messaging

if not firebase_admin._apps:
    # Initialize Firebase with service account
    service_account_json = os.getenv('SERVICE_ACCOUNT_JSON')
    if service_account_json:
        service_account_info = json.loads(service_account_json)
        cred = credentials.Certificate(service_account_info)
        firebase_admin.initialize_app(cred)
        print("✅ Firebase initialized successfully")
    else:
        print("❌ SERVICE_ACCOUNT_JSON not found in environment variables")
        sys.exit(1)

# Import after Firebase initialization
import database.notifications as notification_db
import database.conversations as conversations_db
import database.chat as chat_db
from models.notification_message import NotificationMessage
from models.conversation import Conversation
from utils.llm.external_integrations import get_conversation_summary
from utils.webhooks import day_summary_webhook

# Target user ID
TARGET_USER_ID = "mcaK5709t3MZAcUpdAeEGrmYgaT2"


async def send_weekly_summary_to_teague():
    """Send iOS-only weekly summary notification to Teague"""
    print(f"📅 Sending iOS-only WEEKLY summary notification to Teague")
    print(f"👤 Target User ID: {TARGET_USER_ID}")
    print("=" * 80)
    
    try:
        # Get user's FCM token
        user_token = notification_db.get_token_only(TARGET_USER_ID)
        if not user_token:
            print(f"❌ No FCM token found for user {TARGET_USER_ID}")
            return False
        
        print(f"🔑 FCM Token: {user_token[:20]}...")
        
        # Calculate date range for the past 7 days
        end_date = datetime.now()
        start_date = datetime.combine((end_date - timedelta(days=7)).date(), time.min)
        
        print(f"📅 Fetching conversations from the past 7 days...")
        print(f"   Start: {start_date.strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"   End: {end_date.strftime('%Y-%m-%d %H:%M:%S')}")
        
        # Get user's conversations for the past week
        memories_data = conversations_db.filter_conversations_by_date(
            TARGET_USER_ID, 
            start_date, 
            end_date
        )
        
        if not memories_data:
            print("⚠️ No conversations found for the past week")
            summary = "No conversations recorded this week. Remember to wear your Memorion device to capture meaningful moments throughout the week!"
        else:
            print(f"📝 Found {len(memories_data)} conversation(s) for the past week")
            
            # Convert dictionary data to Conversation objects
            memories = []
            for memory_dict in memories_data:
                try:
                    memory = Conversation(**memory_dict)
                    memories.append(memory)
                except Exception as e:
                    print(f"⚠️ Error converting memory to Conversation object: {e}")
                    continue

            if not memories:
                summary = "No valid conversations found for the past week."
            else:
                print("🤖 Generating weekly conversation summary...")
                # Generate weekly summary using the existing function but with weekly context
                summary = generate_weekly_summary(TARGET_USER_ID, memories)
        
        print(f"📄 Weekly summary generated ({len(summary)} characters)")
        print(f"Preview: {summary[:200]}...")
        
        # Create notification message for navigation
        ai_message = NotificationMessage(
            text=summary,
            from_integration='false',
            type='week_summary',
            notification_type='weekly_summary',
            navigate_to="/chat/omi",
        )

        # Store the summary in chat with 'omi' app_id so it appears in /chat/omi
        print("💾 Storing weekly summary in chat with app_id='omi'...")
        store_weekly_summary_message(summary, TARGET_USER_ID)
        
        # Send webhook (in background) - using day_summary_webhook for now
        print("🔗 Triggering webhook...")
        threading.Thread(target=day_summary_webhook, args=(TARGET_USER_ID, summary)).start()
        
        # Send iOS-specific notification
        success = await send_ios_weekly_notification(user_token, summary, ai_message)
        
        if success:
            print("✅ SUCCESS! iOS weekly notification sent to Teague")
            print("💡 User can now tap notification to view weekly summary")
            return True
        else:
            print("❌ Failed to send iOS weekly notification")
            return False
                    
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False


def store_weekly_summary_message(text: str, uid: str):
    """Store weekly summary message in the session the user is actually using"""
    from models.chat import Message, ChatSession
    import uuid
    from datetime import timezone

    # First, find the session the user is actually using (where they see messages)
    print("🔍 Finding the active chat session the user is actually using...")

    # Check for session with app_id=None (where the user's messages are)
    active_session = chat_db.get_chat_session(uid, app_id=None)

    if active_session:
        app_id_to_use = None
        session_to_use = active_session
        print(f"✅ Found active session with app_id=None: {active_session['id']}")
        print(f"   This is where the user sees their chat messages")
    else:
        # Fallback to 'omi' session
        print("🔍 No session with app_id=None found, using 'omi' session...")
        app_id_to_use = 'omi'
        session_to_use = chat_db.get_chat_session(uid, app_id='omi')

        if not session_to_use:
            print("📝 Creating new 'omi' session...")
            session_data = {
                'id': str(uuid.uuid4()),
                'created_at': datetime.now(timezone.utc),
                'plugin_id': 'omi',
                'app_id': 'omi',
                'message_ids': [],
                'file_ids': [],
                'title': 'Chat with Omi'
            }
            session_to_use = chat_db.add_chat_session(uid, session_data)
            print(f"✅ Created new 'omi' session: {session_to_use['id']}")
        else:
            print(f"✅ Found existing 'omi' session: {session_to_use['id']}")

    # Create the message with proper session association
    message_id = str(uuid.uuid4())
    ai_message = Message(
        id=message_id,
        text=text,
        created_at=datetime.now(timezone.utc),
        sender='ai',
        app_id=app_id_to_use,  # Use the same app_id as the active session
        chat_session_id=session_to_use['id'],  # Associate with the active session
        from_external_integration=False,
        type='day_summary',  # Keep as day_summary for compatibility
        memories_id=[],
    )

    # Store the message
    chat_db.add_message(uid, ai_message.dict())

    # Add message to chat session
    chat_db.add_message_to_chat_session(uid, session_to_use['id'], message_id)

    print(f"✅ Weekly summary stored with app_id='{app_id_to_use}' and chat_session_id='{session_to_use['id']}'")
    print(f"✅ Message will appear in the user's active chat session")
    return ai_message


def generate_weekly_summary(uid: str, memories: list) -> str:
    """Generate a comprehensive weekly summary from conversations"""
    from utils.llms.memory import get_prompt_memories
    import database.users as users_db
    from models.other import Person
    from utils.llm.clients import llm_medium
    
    user_name, memories_str = get_prompt_memories(uid)

    all_person_ids = []
    for m in memories:
        all_person_ids.extend([s.person_id for s in m.transcript_segments if s.person_id])

    people = []
    if all_person_ids:
        people_data = users_db.get_people_by_ids(uid, list(set(all_person_ids)))
        people = [Person(**p) for p in people_data]

    conversation_history = Conversation.conversations_to_string(memories, people=people)

    # Calculate week date range for context
    end_date = datetime.now()
    start_date = end_date - timedelta(days=7)
    week_range = f"{start_date.strftime('%B %d')} - {end_date.strftime('%B %d, %Y')}"

    prompt = f"""
You are an experienced mentor and reflection coach who helps people gain insights from their weekly interactions.
You are creating a comprehensive weekly summary for {user_name}. {memories_str}

Based on {user_name}'s conversations from the past week ({week_range}), create a thoughtful weekly summary with exactly 6 sections.
Use the same language as the conversations (Chinese if conversations are in Chinese, English if in English, etc.).

Format your response with clear section headers and organize the content as follows:

**Weekly Highlights**
Identify the most interesting, memorable, or significant moments from this week's conversations. Include direct quotes from the actual transcripts to support each highlight. Focus on moments that stood out as particularly engaging, insightful, or important throughout the week.

**Key Follow-up Items**
List specific action items, tasks, or commitments that {user_name} needs to follow up on based on this week's conversations. Present these as numbered items for clarity, prioritizing by importance and urgency.

**Decisions Made This Week**
Summarize important choices, conclusions, or commitments that were decided during this week's conversations. Include context about what led to each decision and why it was significant for the week.

**Weekly Learning**
Capture knowledge, insights, skills, or information that {user_name} learned during this week's conversations. This could include technical concepts, business insights, personal realizations, or new perspectives gained over the week.

**Weekly Patterns & Insights**
Analyze conversation patterns, recurring themes, or behavioral insights from the week. Identify trends in communication style, topics of interest, or relationship dynamics that emerged across multiple conversations.

**Areas for Growth**
Provide thoughtful self-reflection on communication style, behavior, or interactions that could be enhanced based on the week's conversations. Include specific examples and focus on constructive feedback for personal growth in the coming week.

**This Week's Conversations:**
```
{conversation_history}
```

Create a comprehensive but concise weekly summary that is personalized to {user_name}'s actual conversation content. Ensure each section contains meaningful insights drawn directly from the provided conversations, not generic templates. Focus on weekly patterns and cumulative insights rather than daily details.
The response should be in the same language of conversations content.
""".replace('    ', '').strip()

    return llm_medium.invoke(prompt).content


async def send_ios_weekly_notification(fcm_token: str, summary: str, ai_message: NotificationMessage):
    """Send iOS-specific FCM notification for weekly summary with proper APNS configuration"""
    
    weekly_summary_title = "Your weekly conversation insights"
    
    # Truncate summary for notification display
    notification_body = summary[:100] + "..." if len(summary) > 100 else summary
    
    print("📱 Creating iOS-specific weekly FCM message...")
    
    # Create basic notification
    notification = messaging.Notification(
        title=weekly_summary_title,
        body=notification_body
    )
    
    # iOS-specific APNS configuration
    apns_config = messaging.APNSConfig(
        headers={
            'apns-priority': '10',  # High priority for immediate delivery
            'apns-push-type': 'alert'
        },
        payload=messaging.APNSPayload(
            aps=messaging.Aps(
                alert=messaging.ApsAlert(
                    title=weekly_summary_title,
                    body=notification_body
                ),
                badge=1,  # Show badge on app icon
                sound='default',  # Use default notification sound
                category='WEEKLY_SUMMARY',  # Custom category for iOS
                # iOS-specific: content available for background processing
                content_available=True
            ),
            # Custom payload for app-specific data
            custom_data={
                'summary_length': str(len(summary)),
                'user_id': TARGET_USER_ID,
                'timestamp': str(datetime.now().isoformat()),
                'summary_type': 'weekly'
            }
        )
    )
    
    # Create message with iOS config and navigation data
    message = messaging.Message(
        notification=notification,
        token=fcm_token,
        data=NotificationMessage.get_message_as_dict(ai_message),
        apns=apns_config  # iOS-specific configuration
        # Note: No android config - iOS only!
    )
    
    try:
        print("🚀 Sending iOS weekly FCM message...")
        response = messaging.send(message)
        print(f'✅ iOS weekly notification sent successfully!')
        print(f'📨 FCM Response: {response}')
        print(f'🎯 Weekly notification details:')
        print(f'   - Title: {weekly_summary_title}')
        print(f'   - Body: {notification_body}')
        print(f'   - Badge: 1')
        print(f'   - Sound: default')
        print(f'   - Category: WEEKLY_SUMMARY')
        print(f'   - Navigation: /chat/omi')
        print(f'   - Summary Type: weekly')
        return True
        
    except Exception as e:
        error_message = str(e)
        if "Requested entity was not found" in error_message:
            print(f'❌ FCM Token not found or invalid: {fcm_token[:20]}...')
            print("💡 User may need to reinstall the app or re-login")
        elif "not a valid FCM registration token" in error_message:
            print(f'❌ Invalid FCM token format: {fcm_token[:20]}...')
        else:
            print(f'❌ iOS weekly notification failed: {e}')
        
        print("🔍 Troubleshooting tips:")
        print("   1. Check if user has notifications enabled")
        print("   2. Verify FCM token is still valid")
        print("   3. Ensure app is installed and logged in")
        print("   4. Check if weekly notifications are supported in app")
        return False


def main():
    """Main function"""
    print("📅 iOS-Only Weekly Summary Notification Sender for Teague")
    print("=" * 80)
    print(f"Target User: {TARGET_USER_ID}")
    print("Platform: iOS only (Android notifications disabled)")
    print("Timeframe: Past 7 days")
    print()
    
    # Run the notification sender
    success = asyncio.run(send_weekly_summary_to_teague())
    
    if success:
        print("\n🎉 Mission accomplished! Teague should receive the iOS weekly notification.")
        print("📱 The notification includes comprehensive weekly insights and patterns.")
    else:
        print("\n💥 Mission failed! Check the logs above for details.")
    
    return success


if __name__ == "__main__":
    main()
