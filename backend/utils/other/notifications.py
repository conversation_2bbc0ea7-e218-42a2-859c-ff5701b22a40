import asyncio
import concurrent.futures
import threading
from datetime import datetime
from datetime import time

import pytz

import database.chat as chat_db
import database.conversations as conversations_db
import database.notifications as notification_db
from models.notification_message import NotificationMessage
from utils.llm.external_integrations import get_conversation_summary
from utils.notifications import send_notification, send_bulk_notification
from utils.webhooks import day_summary_webhook


def store_daily_summary_message_in_active_session(text: str, uid: str):
    """Store daily summary message in the session the user is actually using"""
    from models.chat import Message, ChatSession
    import uuid
    from datetime import timezone, datetime

    # First, find the session the user is actually using (where they see messages)
    print(f"🔍 Finding active chat session for user {uid}...")

    # Check for session with app_id=None (where the user's messages are)
    active_session = chat_db.get_chat_session(uid, app_id=None)

    if active_session:
        app_id_to_use = None
        session_to_use = active_session
        print(f"✅ Found active session with app_id=None: {active_session['id']}")
    else:
        # Fallback to 'omi' session
        print("🔍 No session with app_id=None found, using 'omi' session...")
        app_id_to_use = 'omi'
        session_to_use = chat_db.get_chat_session(uid, app_id='omi')

        if not session_to_use:
            print("📝 Creating new 'omi' session...")
            session_data = {
                'id': str(uuid.uuid4()),
                'created_at': datetime.now(timezone.utc),
                'plugin_id': 'omi',
                'app_id': 'omi',
                'message_ids': [],
                'file_ids': [],
                'title': 'Chat with Omi'
            }
            session_to_use = chat_db.add_chat_session(uid, session_data)
            print(f"✅ Created new 'omi' session: {session_to_use['id']}")
        else:
            print(f"✅ Found existing 'omi' session: {session_to_use['id']}")

    # Create the message with proper session association
    message_id = str(uuid.uuid4())
    ai_message = Message(
        id=message_id,
        text=text,
        created_at=datetime.now(timezone.utc),
        sender='ai',
        app_id=app_id_to_use,  # Use the same app_id as the active session
        chat_session_id=session_to_use['id'],  # Associate with the active session
        from_external_integration=False,
        type='day_summary',  # Keep as day_summary for compatibility
        memories_id=[],
    )

    # Store the message
    chat_db.add_message(uid, ai_message.dict())

    # Add message to chat session
    chat_db.add_message_to_chat_session(uid, session_to_use['id'], message_id)

    print(f"✅ Daily summary stored with app_id='{app_id_to_use}' and chat_session_id='{session_to_use['id']}'")
    return ai_message


async def start_cron_job():
    print(f'start_cron_job called at {datetime.now(pytz.utc)}')
    if should_run_job():
        print('start_cron_job: should_run_job returned True, sending notifications...')
        try:
            await send_daily_notification()
            await send_daily_summary_notification()
            print('start_cron_job: All notifications sent successfully')
        except Exception as e:
            print(f'Error in start_cron_job: {e}')
            import traceback
            traceback.print_exc()
    else:
        print('start_cron_job: should_run_job returned False, no notifications to send')


def should_run_job():
    current_utc = datetime.now(pytz.utc)
    target_hours = {8, 22}
    print(f'should_run_job: Current UTC time: {current_utc}')

    matching_timezones = []
    for tz in pytz.all_timezones:
        try:
            local_time = current_utc.astimezone(pytz.timezone(tz))
            if local_time.hour in target_hours and local_time.minute == 0:
                matching_timezones.append(f"{tz} ({local_time.strftime('%H:%M')})")
        except Exception:
            # Skip invalid timezones
            continue

    if matching_timezones:
        print(f'should_run_job: Found {len(matching_timezones)} timezones at target hours: {matching_timezones[:5]}...')
        return True

    print('should_run_job: No timezones found at target hours (8 AM or 10 PM)')
    return False


async def send_daily_summary_notification():
    """Enhanced daily summary notification with improved logging and error handling"""
    try:
        print("🌅 Starting enhanced daily summary notification process...")
        daily_summary_target_time = "22:00"

        print(f"🕙 Target time: {daily_summary_target_time}")
        timezones_in_time = _get_timezones_at_time(daily_summary_target_time)
        print(f"🌍 Found {len(timezones_in_time)} timezones at target time")

        user_in_time_zone = await notification_db.get_users_id_in_timezones(timezones_in_time)
        if not user_in_time_zone:
            print("⚠️ No users found in target timezones")
            return None

        print(f"👥 Found {len(user_in_time_zone)} users to notify")
        await _send_bulk_summary_notification(user_in_time_zone)

        print("🎉 Enhanced daily summary notification process completed successfully")
        return user_in_time_zone

    except Exception as e:
        print(f"❌ Error in enhanced daily summary notification: {e}")
        import traceback
        traceback.print_exc()
        return None


def _send_summary_notification(user_data: tuple):
    """Enhanced summary notification with improved error handling and logging"""
    uid = user_data[0]
    fcm_token = user_data[1]
    daily_summary_title = "Here is your action plan for tomorrow"

    print(f"📱 Processing enhanced summary notification for user {uid[:8]}...")

    try:
        # Get user's conversations for today
        memories_data = conversations_db.filter_conversations_by_date(
            uid, datetime.combine(datetime.now().date(), time.min), datetime.now()
        )

        if not memories_data:
            print(f"⚠️ No conversations found for user {uid[:8]}, skipping notification")
            return

        # Convert dictionary data to Conversation objects
        from models.conversation import Conversation
        memories = []
        for memory_dict in memories_data:
            try:
                memory = Conversation(**memory_dict)
                memories.append(memory)
            except Exception as e:
                print(f"⚠️ Error converting memory to Conversation object for user {uid[:8]}: {e}")
                continue

        if not memories:
            print(f"⚠️ No valid conversations found for user {uid[:8]}, skipping notification")
            return

        print(f"📝 Found {len(memories)} conversation(s) for user {uid[:8]}")

        # Generate summary
        print(f"🤖 Generating conversation summary for user {uid[:8]}...")
        summary = get_conversation_summary(uid, memories)

        if not summary:
            print(f"⚠️ Failed to generate summary for user {uid[:8]}, skipping notification")
            return

        print(f"📄 Summary generated for user {uid[:8]} ({len(summary)} characters)")

        # Create enhanced notification message
        ai_message = NotificationMessage(
            text=summary,
            from_integration='false',
            type='day_summary',
            notification_type='daily_summary',
            navigate_to="/chat/omi",  # omi ~ no select
        )

        # Store the summary in chat using enhanced session logic
        print(f"💾 Storing enhanced summary in chat for user {uid[:8]}...")
        store_daily_summary_message_in_active_session(summary, uid)

        # Send webhook in background
        print(f"🔗 Triggering webhook for user {uid[:8]}...")
        threading.Thread(target=day_summary_webhook, args=(uid, summary)).start()

        # Send enhanced notification with improved error handling
        print(f"🚀 Sending enhanced notification to user {uid[:8]}...")
        success = send_notification(
            fcm_token,
            daily_summary_title,
            summary,
            NotificationMessage.get_message_as_dict(ai_message)
        )

        if success:
            print(f"✅ Enhanced notification successfully sent to user {uid[:8]}")
        else:
            print(f"❌ Enhanced notification failed for user {uid[:8]}")

    except Exception as e:
        print(f"❌ Error in enhanced summary notification for user {uid[:8]}: {e}")
        import traceback
        traceback.print_exc()


async def _send_bulk_summary_notification(users: list):
    """Enhanced bulk summary notification with improved logging and error tracking"""
    print(f"📤 Starting enhanced bulk summary notifications for {len(users)} users")

    loop = asyncio.get_running_loop()
    with concurrent.futures.ThreadPoolExecutor() as pool:
        tasks = [loop.run_in_executor(pool, _send_summary_notification, uid) for uid in users]

        try:
            await asyncio.gather(*tasks)
            print(f"✅ Enhanced bulk summary notifications completed for {len(users)} users")
        except Exception as e:
            print(f"❌ Error in enhanced bulk summary notifications: {e}")
            import traceback
            traceback.print_exc()


async def send_daily_notification():
    try:
        print("Starting send_daily_notification...")
        morning_alert_title = "Memorion"
        morning_alert_body = "Wear your Memorion device to capture your conversations today."
        morning_target_time = "08:00"

        result = await _send_notification_for_time(morning_target_time, morning_alert_title, morning_alert_body)
        print(f"send_daily_notification completed. Users notified: {len(result) if result else 0}")
        return result

    except Exception as e:
        print(f"Error in send_daily_notification: {e}")
        import traceback
        traceback.print_exc()
        return None


async def _send_notification_for_time(target_time: str, title: str, body: str):
    user_in_time_zone = await _get_users_in_timezone(target_time)
    if not user_in_time_zone:
        print("No users found in time zone")
        return None
    await send_bulk_notification(user_in_time_zone, title, body)
    return user_in_time_zone


async def _get_users_in_timezone(target_time: str):
    timezones_in_time = _get_timezones_at_time(target_time)
    return await notification_db.get_users_token_in_timezones(timezones_in_time)


def _get_timezones_at_time(target_time):
    target_timezones = []
    for tz_name in pytz.all_timezones:
        tz = pytz.timezone(tz_name)
        current_time = datetime.now(tz).strftime("%H:%M")
        if current_time == target_time:
            target_timezones.append(tz_name)
    return target_timezones
