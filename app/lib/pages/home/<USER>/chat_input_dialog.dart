import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:omi/pages/chat/page.dart';
import 'package:omi/providers/connectivity_provider.dart';
import 'package:omi/providers/message_provider.dart';
import 'package:omi/providers/chat_session_provider.dart';

import 'package:provider/provider.dart';

class ChatInputDialog extends StatefulWidget {
  final VoidCallback? onClose;
  final bool isPersistent;

  const ChatInputDialog({
    super.key,
    this.onClose,
    this.isPersistent = false,
  });

  @override
  State<ChatInputDialog> createState() => _ChatInputDialogState();
}

// Global key to access the chat input dialog state
final GlobalKey chatInputDialogKey = GlobalKey();

// Helper function to focus the chat input
void focusChatInput() {
  final state = chatInputDialogKey.currentState;
  if (state != null && state is _ChatInputDialogState) {
    state.focusInput();
  }
}

class _ChatInputDialogState extends State<ChatInputDialog> {
  final TextEditingController _textController = TextEditingController();
  final FocusNode _focusNode = FocusNode();

  @override
  void initState() {
    super.initState();
    // Auto-focus the text field when dialog opens (only for non-persistent mode)
    if (!widget.isPersistent) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _focusNode.requestFocus();
      });
    }
  }

  // Method to focus the input field from external sources
  void focusInput() {
    if (mounted) {
      _focusNode.requestFocus();
    }
  }

  @override
  void dispose() {
    _textController.dispose();
    _focusNode.dispose();
    super.dispose();
  }

  void _sendMessage() async {
    final text = _textController.text.trim();
    if (text.isEmpty) return;

    final connectivityProvider = context.read<ConnectivityProvider>();
    if (!connectivityProvider.isConnected) {
      ConnectivityProvider.showNoInternetDialog(context);
      return;
    }

    // Store the message text
    final messageText = text;

    // Close the dialog first
    widget.onClose?.call();

    // Navigate to chat page with the message
    if (mounted) {
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => ChatPageWithInitialMessage(
            initialMessage: messageText,
          ),
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<ConnectivityProvider>(
      builder: (context, connectivityProvider, child) {
        return Container(
          height: widget.isPersistent ? double.infinity : null,
          margin: widget.isPersistent ? EdgeInsets.zero : const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          padding: widget.isPersistent
              ? const EdgeInsets.fromLTRB(12, 8, 12, 12) // Reduced padding for persistent mode
              : const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: widget.isPersistent
                ? Colors.transparent // Transparent for persistent mode (parent container handles background)
                : const Color(0xFF1F1F25),
            borderRadius: widget.isPersistent
                ? const BorderRadius.only(
                    topLeft: Radius.circular(16),
                    topRight: Radius.circular(16),
                  )
                : BorderRadius.circular(16),
            boxShadow: widget.isPersistent
                ? []
                : [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.3),
                      blurRadius: 8,
                      offset: const Offset(0, 4),
                    ),
                  ],
          ),
          child: Column(
            mainAxisSize: widget.isPersistent ? MainAxisSize.max : MainAxisSize.min,
            children: [
              const SizedBox(height: 12),
              // Input field and send button
              Row(
                children: [
                  Expanded(
                    child: TextField(
                      controller: _textController,
                      focusNode: _focusNode,
                      maxLines: null,
                      keyboardType: TextInputType.multiline,
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 14,
                      ),
                      decoration: InputDecoration(
                        hintText: '💬 Chat to your second brain...',
                        hintStyle: const TextStyle(
                          color: Colors.white60,
                          fontSize: 14,
                        ),
                        filled: true,
                        fillColor: widget.isPersistent
                            ? Colors.white
                                .withOpacity(0.12) // More opaque for better visibility on semi-transparent background
                            : Colors.white.withOpacity(0.05),
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12),
                          borderSide: BorderSide.none,
                        ),
                        contentPadding: widget.isPersistent
                            ? const EdgeInsets.symmetric(
                                horizontal: 14, vertical: 10) // Reduced padding for compact design
                            : const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                      ),
                      onSubmitted: (_) => _sendMessage(),
                    ),
                  ),
                  const SizedBox(width: 12),
                  ValueListenableBuilder<TextEditingValue>(
                    valueListenable: _textController,
                    builder: (context, value, child) {
                      final canSend = value.text.trim().isNotEmpty && connectivityProvider.isConnected;

                      return GestureDetector(
                        onTap: canSend ? _sendMessage : null,
                        child: AnimatedContainer(
                          duration: const Duration(milliseconds: 200),
                          padding: widget.isPersistent
                              ? const EdgeInsets.all(10) // Reduced padding for compact design
                              : const EdgeInsets.all(12),
                          decoration: BoxDecoration(
                            color: canSend ? Colors.deepPurple : Colors.white.withOpacity(0.1),
                            borderRadius: BorderRadius.circular(12),
                            boxShadow: canSend
                                ? [
                                    BoxShadow(
                                      color: Colors.deepPurple.withOpacity(0.3),
                                      blurRadius: 8,
                                      offset: const Offset(0, 4),
                                    ),
                                  ]
                                : [],
                          ),
                          child: Icon(
                            Icons.send_rounded,
                            color: canSend ? Colors.white : Colors.white38,
                            size: 18,
                          ),
                        ),
                      );
                    },
                  ),
                ],
              ),
            ],
          ),
        );
      },
    );
  }
}

class ChatPageWithInitialMessage extends StatefulWidget {
  final String initialMessage;

  const ChatPageWithInitialMessage({
    super.key,
    required this.initialMessage,
  });

  @override
  State<ChatPageWithInitialMessage> createState() => _ChatPageWithInitialMessageState();
}

class _ChatPageWithInitialMessageState extends State<ChatPageWithInitialMessage> {
  @override
  void initState() {
    super.initState();
    // Send the initial message after the chat page is built
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        final messageProvider = context.read<MessageProvider>();
        final sessionProvider = context.read<ChatSessionProvider>();

        // Send the message using the same pattern as the chat page
        messageProvider.setSendingMessage(true);
        messageProvider.addMessageLocally(widget.initialMessage);
        messageProvider.sendMessageStreamToServer(
          widget.initialMessage,
          chatSessionId: sessionProvider.currentSessionId,
          context: context,
        );
        messageProvider.setSendingMessage(false);
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return const ChatPage(isPivotBottom: false);
  }
}
